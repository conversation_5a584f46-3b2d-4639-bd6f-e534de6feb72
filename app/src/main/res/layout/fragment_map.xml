<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.amap.api.maps.MapView
        android:id="@+id/map_view"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- 同步模式切换按钮 -->
    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_toggle_sync_mode"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        android:text="自动同步"
        android:textSize="12sp"
        app:cornerRadius="20dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        style="@style/Widget.Material3.Button.OutlinedButton" />

    <!-- 手动同步位置按钮 -->
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fab_sync_location"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        android:src="@drawable/ic_sync"
        android:contentDescription="同步位置"
        app:layout_constraintBottom_toTopOf="@+id/fab_emergency"
        app:layout_constraintEnd_toEndOf="parent"
        app:tint="@android:color/white" />

    <!-- 紧急救援按钮 -->
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fab_emergency"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        android:src="@drawable/ic_emergency"
        android:contentDescription="紧急救援"
        android:backgroundTint="@color/emergency_color"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:tint="@android:color/white" />

    <!-- 状态信息卡片 -->
    <com.google.android.material.card.MaterialCardView
        android:id="@+id/card_status"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        app:cardCornerRadius="8dp"
        app:cardElevation="4dp"
        app:layout_constraintEnd_toStartOf="@+id/fab_sync_location"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="12dp">

            <TextView
                android:id="@+id/tv_online_count"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="在线: 0人"
                android:textSize="12sp"
                android:textColor="@color/text_primary" />

            <TextView
                android:id="@+id/tv_last_sync"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="最后同步: --"
                android:textSize="12sp"
                android:textColor="@color/text_secondary"
                android:gravity="end" />

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

</androidx.constraintlayout.widget.ConstraintLayout>
