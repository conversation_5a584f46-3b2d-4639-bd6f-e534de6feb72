package com.rescue.app.ui.login

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.launch

class LoginViewModel : ViewModel() {
    
    private val _loginResult = MutableLiveData<LoginResult>()
    val loginResult: LiveData<LoginResult> = _loginResult
    
    fun login(username: String, password: String) {
        viewModelScope.launch {
            try {
                _loginResult.value = LoginResult.Loading
                
                // TODO: 实现实际的登录逻辑
                // 这里先模拟登录过程
                kotlinx.coroutines.delay(2000) // 模拟网络请求
                
                // 模拟登录验证
                if (username == "admin" && password == "123456") {
                    _loginResult.value = LoginResult.Success("登录成功")
                } else {
                    _loginResult.value = LoginResult.Error("用户名或密码错误")
                }
                
            } catch (e: Exception) {
                _loginResult.value = LoginResult.Error("登录失败: ${e.message}")
            }
        }
    }
}

sealed class LoginResult {
    object Loading : LoginResult()
    data class Success(val message: String) : LoginResult()
    data class Error(val message: String) : LoginResult()
}
