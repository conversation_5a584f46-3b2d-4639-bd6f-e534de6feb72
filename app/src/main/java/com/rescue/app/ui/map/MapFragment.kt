package com.rescue.app.ui.map

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.Observer
import com.amap.api.maps.AMap
import com.amap.api.maps.CameraUpdateFactory
import com.amap.api.maps.MapView
import com.amap.api.maps.model.BitmapDescriptorFactory
import com.amap.api.maps.model.LatLng
import com.amap.api.maps.model.Marker
import com.amap.api.maps.model.MarkerOptions
import com.amap.api.maps.model.MyLocationStyle
import com.rescue.app.R
import com.rescue.app.databinding.FragmentMapBinding
import com.rescue.app.data.local.entity.TeamMateLocation

class MapFragment : Fragment() {
    
    private var _binding: FragmentMapBinding? = null
    private val binding get() = _binding!!
    
    private val viewModel: MapViewModel by viewModels()
    
    private lateinit var mapView: MapView
    private lateinit var aMap: AMap
    private val teamMateMarkers = mutableMapOf<String, Marker>()
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentMapBinding.inflate(inflater, container, false)
        return binding.root
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        mapView = binding.mapView
        mapView.onCreate(savedInstanceState)
        
        initMap()
        setupUI()
        observeViewModel()
        
        viewModel.startLocationUpdates()
    }
    
    private fun initMap() {
        aMap = mapView.map
        
        // 设置地图类型
        aMap.mapType = AMap.MAP_TYPE_NORMAL
        
        // 设置定位样式
        val myLocationStyle = MyLocationStyle()
        myLocationStyle.myLocationType(MyLocationStyle.LOCATION_TYPE_LOCATION_ROTATE_NO_CENTER)
        myLocationStyle.strokeColor(resources.getColor(R.color.primary_color, null))
        myLocationStyle.radiusFillColor(resources.getColor(R.color.primary_color_alpha, null))
        aMap.myLocationStyle = myLocationStyle
        
        // 启用定位
        aMap.isMyLocationEnabled = true
        
        // 设置缩放级别
        aMap.moveCamera(CameraUpdateFactory.zoomTo(15f))
        
        // 设置地图点击监听
        aMap.setOnMapClickListener { latLng ->
            viewModel.onMapClick(latLng.latitude, latLng.longitude)
        }
    }
    
    private fun setupUI() {
        // 位置同步按钮
        binding.fabSyncLocation.setOnClickListener {
            viewModel.syncLocationManually()
        }
        
        // 发起救援按钮
        binding.fabEmergency.setOnClickListener {
            viewModel.requestEmergencyRescue()
        }
        
        // 切换同步模式按钮
        binding.btnToggleSyncMode.setOnClickListener {
            viewModel.toggleSyncMode()
        }
    }
    
    private fun observeViewModel() {
        // 观察当前位置
        viewModel.currentLocation.observe(viewLifecycleOwner, Observer { location ->
            location?.let {
                val latLng = LatLng(it.latitude, it.longitude)
                aMap.animateCamera(CameraUpdateFactory.newLatLngZoom(latLng, 15f))
            }
        })
        
        // 观察队友位置
        viewModel.teamMateLocations.observe(viewLifecycleOwner, Observer { locations ->
            updateTeamMateMarkers(locations)
        })
        
        // 观察同步模式
        viewModel.syncMode.observe(viewLifecycleOwner, Observer { mode ->
            binding.btnToggleSyncMode.text = if (mode == SyncMode.AUTO) "自动同步" else "手动同步"
        })
        
        // 观察消息
        viewModel.message.observe(viewLifecycleOwner, Observer { message ->
            // TODO: 显示Toast或Snackbar
        })
    }
    
    private fun updateTeamMateMarkers(locations: List<TeamMateLocation>) {
        // 清除旧的标记
        teamMateMarkers.values.forEach { it.remove() }
        teamMateMarkers.clear()
        
        // 添加新的标记
        locations.forEach { location ->
            val latLng = LatLng(location.latitude, location.longitude)
            val marker = aMap.addMarker(
                MarkerOptions()
                    .position(latLng)
                    .title(location.nickname)
                    .snippet("状态: ${location.status}")
                    .icon(BitmapDescriptorFactory.defaultMarker(BitmapDescriptorFactory.HUE_BLUE))
            )
            marker?.let {
                teamMateMarkers[location.userId] = it
            }
        }
    }
    
    override fun onResume() {
        super.onResume()
        mapView.onResume()
    }
    
    override fun onPause() {
        super.onPause()
        mapView.onPause()
    }
    
    override fun onDestroy() {
        super.onDestroy()
        mapView.onDestroy()
        _binding = null
    }
    
    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        mapView.onSaveInstanceState(outState)
    }
}
