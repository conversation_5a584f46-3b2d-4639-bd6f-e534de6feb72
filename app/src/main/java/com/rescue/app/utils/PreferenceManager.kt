package com.rescue.app.utils

import android.content.Context
import android.content.SharedPreferences

class PreferenceManager(context: Context) {
    
    companion object {
        private const val PREF_NAME = "rescue_app_prefs"
        
        // 用户相关
        private const val KEY_IS_LOGGED_IN = "is_logged_in"
        private const val KEY_USER_ID = "user_id"
        private const val KEY_USERNAME = "username"
        private const val KEY_NICKNAME = "nickname"
        private const val KEY_TOKEN = "token"
        
        // 设置相关
        private const val KEY_SYNC_MODE = "sync_mode"
        private const val KEY_SYNC_INTERVAL = "sync_interval"
        private const val KEY_AUTO_ACCEPT_MISSION = "auto_accept_mission"
        
        // 位置相关
        private const val KEY_LAST_LATITUDE = "last_latitude"
        private const val KEY_LAST_LONGITUDE = "last_longitude"
        private const val KEY_LAST_SYNC_TIME = "last_sync_time"
    }
    
    private val sharedPreferences: SharedPreferences = 
        context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE)
    
    // 用户登录状态
    fun isLoggedIn(): Boolean = sharedPreferences.getBoolean(KEY_IS_LOGGED_IN, false)
    
    fun setLoggedIn(isLoggedIn: Boolean) {
        sharedPreferences.edit().putBoolean(KEY_IS_LOGGED_IN, isLoggedIn).apply()
    }
    
    // 用户信息
    fun getUserId(): String? = sharedPreferences.getString(KEY_USER_ID, null)
    
    fun setUserId(userId: String) {
        sharedPreferences.edit().putString(KEY_USER_ID, userId).apply()
    }
    
    fun getUsername(): String? = sharedPreferences.getString(KEY_USERNAME, null)
    
    fun setUsername(username: String) {
        sharedPreferences.edit().putString(KEY_USERNAME, username).apply()
    }
    
    fun getNickname(): String? = sharedPreferences.getString(KEY_NICKNAME, null)
    
    fun setNickname(nickname: String) {
        sharedPreferences.edit().putString(KEY_NICKNAME, nickname).apply()
    }
    
    fun getToken(): String? = sharedPreferences.getString(KEY_TOKEN, null)
    
    fun setToken(token: String) {
        sharedPreferences.edit().putString(KEY_TOKEN, token).apply()
    }
    
    // 同步模式设置
    fun getSyncMode(): String = sharedPreferences.getString(KEY_SYNC_MODE, "AUTO") ?: "AUTO"
    
    fun setSyncMode(mode: String) {
        sharedPreferences.edit().putString(KEY_SYNC_MODE, mode).apply()
    }
    
    // 同步间隔（秒）
    fun getSyncInterval(): Int = sharedPreferences.getInt(KEY_SYNC_INTERVAL, 30)
    
    fun setSyncInterval(interval: Int) {
        sharedPreferences.edit().putInt(KEY_SYNC_INTERVAL, interval).apply()
    }
    
    // 自动接收任务
    fun isAutoAcceptMission(): Boolean = sharedPreferences.getBoolean(KEY_AUTO_ACCEPT_MISSION, false)
    
    fun setAutoAcceptMission(autoAccept: Boolean) {
        sharedPreferences.edit().putBoolean(KEY_AUTO_ACCEPT_MISSION, autoAccept).apply()
    }
    
    // 最后位置信息
    fun getLastLatitude(): Double = sharedPreferences.getString(KEY_LAST_LATITUDE, "0.0")?.toDoubleOrNull() ?: 0.0
    
    fun getLastLongitude(): Double = sharedPreferences.getString(KEY_LAST_LONGITUDE, "0.0")?.toDoubleOrNull() ?: 0.0
    
    fun setLastLocation(latitude: Double, longitude: Double) {
        sharedPreferences.edit()
            .putString(KEY_LAST_LATITUDE, latitude.toString())
            .putString(KEY_LAST_LONGITUDE, longitude.toString())
            .apply()
    }
    
    // 最后同步时间
    fun getLastSyncTime(): Long = sharedPreferences.getLong(KEY_LAST_SYNC_TIME, 0L)
    
    fun setLastSyncTime(time: Long) {
        sharedPreferences.edit().putLong(KEY_LAST_SYNC_TIME, time).apply()
    }
    
    // 清除所有数据（退出登录时使用）
    fun clear() {
        sharedPreferences.edit().clear().apply()
    }
}
