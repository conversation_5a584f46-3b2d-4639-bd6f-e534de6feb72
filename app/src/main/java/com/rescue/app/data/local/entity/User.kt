package com.rescue.app.data.local.entity

import androidx.room.Entity
import androidx.room.PrimaryKey
import android.os.Parcelable
import kotlinx.parcelize.Parcelize

/**
 * 用户实体类
 */
@Parcelize
@Entity(tableName = "users")
data class User(
    @PrimaryKey
    val id: String,
    val username: String,
    val nickname: String,
    val phone: String,
    val role: String, // 角色：队员、队长、指挥官等
    val teamId: String?, // 所属队伍ID
    val avatar: String?, // 头像URL
    val status: UserStatus = UserStatus.OFFLINE, // 在线状态
    val lastActiveTime: Long = System.currentTimeMillis()
) : Parcelable

/**
 * 用户状态枚举
 */
enum class UserStatus {
    ONLINE,    // 在线
    OFFLINE,   // 离线
    BUSY,      // 忙碌
    EMERGENCY  // 紧急状态
}
