package com.rescue.app.data.local.entity

import androidx.room.Entity
import androidx.room.PrimaryKey
import android.os.Parcelable
import kotlinx.parcelize.Parcelize

/**
 * 位置信息实体类
 */
@Parcelize
@Entity(tableName = "locations")
data class Location(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val userId: String,
    val latitude: Double,
    val longitude: Double,
    val altitude: Double = 0.0,
    val accuracy: Float = 0f,
    val speed: Float = 0f,
    val bearing: Float = 0f,
    val timestamp: Long = System.currentTimeMillis(),
    val address: String? = null,
    val isUploaded: Boolean = false // 是否已上传到服务器
) : Parcelable

/**
 * 队友位置信息
 */
@Parcelize
data class TeamMateLocation(
    val userId: String,
    val username: String,
    val nickname: String,
    val latitude: Double,
    val longitude: Double,
    val timestamp: Long,
    val status: String,
    val avatar: String? = null
) : Parcelable
