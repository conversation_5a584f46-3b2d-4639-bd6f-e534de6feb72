package com.rescue.app.data.local.entity

import androidx.room.Entity
import androidx.room.PrimaryKey
import android.os.Parcelable
import kotlinx.parcelize.Parcelize

/**
 * 搜救任务实体类
 */
@Parcelize
@Entity(tableName = "missions")
data class Mission(
    @PrimaryKey
    val id: String,
    val title: String,
    val description: String,
    val type: MissionType,
    val priority: MissionPriority,
    val status: MissionStatus,
    val creatorId: String,
    val creatorName: String,
    val assigneeIds: List<String> = emptyList(), // 指派的救援人员ID列表
    val targetLatitude: Double?, // 目标位置纬度
    val targetLongitude: Double?, // 目标位置经度
    val targetAddress: String?, // 目标地址描述
    val createTime: Long = System.currentTimeMillis(),
    val startTime: Long? = null,
    val endTime: Long? = null,
    val estimatedDuration: Long? = null, // 预计持续时间（分钟）
    val actualDuration: Long? = null, // 实际持续时间（分钟）
    val notes: String? = null, // 备注信息
    val attachments: List<String> = emptyList() // 附件URL列表
) : Parcelable

/**
 * 任务类型枚举
 */
enum class MissionType {
    SEARCH,     // 搜索任务
    RESCUE,     // 救援任务
    MEDICAL,    // 医疗救助
    EVACUATION, // 疏散任务
    PATROL,     // 巡逻任务
    OTHER       // 其他
}

/**
 * 任务优先级枚举
 */
enum class MissionPriority {
    LOW,        // 低优先级
    MEDIUM,     // 中等优先级
    HIGH,       // 高优先级
    URGENT      // 紧急
}

/**
 * 任务状态枚举
 */
enum class MissionStatus {
    PENDING,    // 待接收
    ACCEPTED,   // 已接收
    IN_PROGRESS,// 进行中
    COMPLETED,  // 已完成
    CANCELLED,  // 已取消
    FAILED      // 失败
}
