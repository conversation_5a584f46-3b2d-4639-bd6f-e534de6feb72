# 搜救系统 Android 应用

## 项目概述
这是一个用于搜救任务管理的Android应用，支持GPS定位、队友位置显示、搜救任务管理等功能。

## 核心功能

### 1. GPS定位功能
- [ ] 当前位置GPS展示 - 在地图上展示自己兵牌所在位置
- [ ] 队友GPS展示 - 在地图上展示附近队友兵牌位置

### 2. 搜救任务管理
- [ ] 接收/执行搜救任务 - 接收来自桌面大屏的搜救任务，并执行搜救任务
- [ ] 发起搜救任务 - 当需要救援时可以发起搜救任务

### 3. 用户认证
- [ ] 登录/登出功能

### 4. 位置同步
- [ ] 自动位置同步模式 - 按照设置间隔时间自动向系统同步GPS坐标信息
- [ ] 手动位置同步模式 - 手动点击同步之后才会向系统同步GPS坐标信息

### 5. 历史轨迹
- [ ] 历史行动轨迹（时间）- 展示一定时间内自己的行动轨迹

## 技术栈
- **开发语言**: Kotlin
- **地图服务**: 高德地图 SDK
- **网络通信**: Retrofit + OkHttp
- **数据存储**: Room Database + SharedPreferences
- **实时通信**: WebSocket
- **架构模式**: MVVM + LiveData + DataBinding

## 开发环境要求
- Android Studio 4.0+
- Android SDK 21+ (Android 5.0+)
- Kotlin 1.5+
- Gradle 7.0+

## 开始开发
1. 使用Android Studio打开项目
2. 同步Gradle依赖
3. 配置地图SDK密钥
4. 运行项目
