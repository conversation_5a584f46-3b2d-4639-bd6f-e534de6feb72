# 搜救系统 Android 开发指南

## 开发环境配置

### 1. 必需软件
- **Android Studio**: 2022.3.1 (Giraffe) 或更高版本
- **JDK**: 11 或更高版本
- **Android SDK**: API Level 21-34
- **Kotlin**: 1.8.0 或更高版本

### 2. 高德地图配置
1. 注册高德开发者账号：https://lbs.amap.com/
2. 创建应用并获取API Key
3. 在 `AndroidManifest.xml` 中配置API Key：
```xml
<meta-data
    android:name="com.amap.api.v2.apikey"
    android:value="YOUR_AMAP_API_KEY" />
```

### 3. 项目导入
1. 使用Android Studio打开项目根目录
2. 等待Gradle同步完成
3. 配置高德地图API Key
4. 运行项目

## 项目架构

### MVVM架构
```
UI Layer (Activity/Fragment)
    ↓
ViewModel Layer
    ↓
Repository Layer
    ↓
Data Layer (Local/Remote)
```

### 目录结构说明
```
app/src/main/java/com/rescue/app/
├── ui/                 # UI层
│   ├── login/         # 登录模块
│   ├── map/           # 地图模块
│   ├── mission/       # 任务模块
│   └── profile/       # 个人中心
├── data/              # 数据层
│   ├── local/         # 本地数据（Room数据库）
│   ├── remote/        # 远程数据（API接口）
│   └── repository/    # 数据仓库
├── domain/            # 业务逻辑层
├── utils/             # 工具类
└── service/           # 后台服务
```

## 核心功能实现

### 1. GPS定位功能
- 使用高德地图SDK获取位置信息
- 实现自动/手动位置同步
- 位置数据本地缓存

### 2. 实时通信
- WebSocket连接管理
- 队友位置实时更新
- 任务状态实时同步

### 3. 任务管理
- 任务列表展示
- 任务接收/完成
- 紧急救援发起

### 4. 数据存储
- Room数据库存储本地数据
- SharedPreferences存储用户设置
- 网络数据缓存策略

## 开发规范

### 1. 代码规范
- 使用Kotlin编程语言
- 遵循Android官方代码规范
- 使用有意义的变量和方法命名
- 添加必要的注释

### 2. Git提交规范
```
feat: 新功能
fix: 修复bug
docs: 文档更新
style: 代码格式调整
refactor: 代码重构
test: 测试相关
chore: 构建过程或辅助工具的变动
```

### 3. 分支管理
- `main`: 主分支，用于发布
- `develop`: 开发分支
- `feature/*`: 功能分支
- `hotfix/*`: 热修复分支

## 测试指南

### 1. 单元测试
- 使用JUnit进行单元测试
- 测试ViewModel和Repository层
- 保持测试覆盖率在80%以上

### 2. UI测试
- 使用Espresso进行UI测试
- 测试关键用户流程
- 模拟各种设备和屏幕尺寸

### 3. 集成测试
- 测试API接口集成
- 测试数据库操作
- 测试WebSocket连接

## 性能优化

### 1. 内存优化
- 避免内存泄漏
- 合理使用图片缓存
- 及时释放资源

### 2. 网络优化
- 使用网络缓存
- 压缩请求数据
- 合理设置超时时间

### 3. 电池优化
- 合理使用后台服务
- 优化位置更新频率
- 使用JobScheduler调度任务

## 发布流程

### 1. 版本管理
- 遵循语义化版本号规范
- 更新版本号和版本名称
- 编写版本更新日志

### 2. 打包发布
- 生成签名APK
- 进行全面测试
- 上传到应用商店

### 3. 监控和反馈
- 集成崩溃监控
- 收集用户反馈
- 持续优化改进

## 常见问题

### 1. 高德地图显示空白
- 检查API Key是否正确配置
- 检查网络权限是否授予
- 检查包名和SHA1是否匹配

### 2. 定位不准确
- 检查定位权限是否授予
- 确保GPS功能已开启
- 在室外环境测试

### 3. WebSocket连接失败
- 检查网络连接状态
- 验证服务器地址和端口
- 检查认证token是否有效

## 联系方式
如有问题，请联系开发团队：
- 邮箱: <EMAIL>
- 微信群: 搜救系统开发群
