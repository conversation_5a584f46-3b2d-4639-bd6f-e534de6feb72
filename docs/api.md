# 搜救系统 API 接口文档

## 基础信息

- **Base URL**: `https://api.rescue-system.com/v1`
- **认证方式**: <PERSON><PERSON>
- **数据格式**: JSON
- **字符编码**: UTF-8

## 通用响应格式

```json
{
  "code": 200,
  "message": "success",
  "data": {},
  "timestamp": 1691234567890
}
```

## 1. 用户认证

### 1.1 用户登录
- **URL**: `/auth/login`
- **Method**: `POST`
- **请求参数**:
```json
{
  "username": "string",
  "password": "string"
}
```
- **响应数据**:
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {
      "id": "user123",
      "username": "admin",
      "nickname": "管理员",
      "phone": "13800138000",
      "role": "commander",
      "teamId": "team001",
      "avatar": "https://example.com/avatar.jpg"
    }
  }
}
```

### 1.2 用户登出
- **URL**: `/auth/logout`
- **Method**: `POST`
- **Headers**: `Authorization: Bearer {token}`

## 2. 位置管理

### 2.1 上传位置信息
- **URL**: `/location/upload`
- **Method**: `POST`
- **Headers**: `Authorization: Bearer {token}`
- **请求参数**:
```json
{
  "latitude": 39.908823,
  "longitude": 116.397470,
  "altitude": 50.0,
  "accuracy": 5.0,
  "speed": 0.0,
  "bearing": 0.0,
  "timestamp": 1691234567890,
  "address": "北京市东城区天安门广场"
}
```

### 2.2 获取队友位置
- **URL**: `/location/teammates`
- **Method**: `GET`
- **Headers**: `Authorization: Bearer {token}`
- **响应数据**:
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "userId": "user456",
      "username": "teammate1",
      "nickname": "队友1",
      "latitude": 39.908823,
      "longitude": 116.397470,
      "timestamp": 1691234567890,
      "status": "online",
      "avatar": "https://example.com/avatar2.jpg"
    }
  ]
}
```

### 2.3 获取历史轨迹
- **URL**: `/location/history`
- **Method**: `GET`
- **Headers**: `Authorization: Bearer {token}`
- **查询参数**:
  - `userId`: 用户ID（可选，默认为当前用户）
  - `startTime`: 开始时间戳
  - `endTime`: 结束时间戳

## 3. 任务管理

### 3.1 获取任务列表
- **URL**: `/missions`
- **Method**: `GET`
- **Headers**: `Authorization: Bearer {token}`
- **查询参数**:
  - `status`: 任务状态（可选）
  - `page`: 页码（默认1）
  - `size`: 每页数量（默认20）

### 3.2 接收任务
- **URL**: `/missions/{missionId}/accept`
- **Method**: `POST`
- **Headers**: `Authorization: Bearer {token}`

### 3.3 完成任务
- **URL**: `/missions/{missionId}/complete`
- **Method**: `POST`
- **Headers**: `Authorization: Bearer {token}`
- **请求参数**:
```json
{
  "notes": "任务完成备注",
  "attachments": ["https://example.com/photo1.jpg"]
}
```

### 3.4 发起救援任务
- **URL**: `/missions/emergency`
- **Method**: `POST`
- **Headers**: `Authorization: Bearer {token}`
- **请求参数**:
```json
{
  "title": "紧急救援",
  "description": "需要紧急救援",
  "type": "RESCUE",
  "priority": "URGENT",
  "targetLatitude": 39.908823,
  "targetLongitude": 116.397470,
  "targetAddress": "北京市东城区天安门广场"
}
```

## 4. WebSocket 实时通信

### 连接地址
`wss://api.rescue-system.com/v1/ws?token={token}`

### 消息格式
```json
{
  "type": "message_type",
  "data": {},
  "timestamp": 1691234567890
}
```

### 消息类型

#### 4.1 位置更新
```json
{
  "type": "location_update",
  "data": {
    "userId": "user123",
    "latitude": 39.908823,
    "longitude": 116.397470,
    "timestamp": 1691234567890
  }
}
```

#### 4.2 新任务通知
```json
{
  "type": "new_mission",
  "data": {
    "mission": {
      "id": "mission123",
      "title": "搜救任务",
      "description": "在指定区域搜救失踪人员",
      "type": "SEARCH",
      "priority": "HIGH"
    }
  }
}
```

#### 4.3 任务状态更新
```json
{
  "type": "mission_status_update",
  "data": {
    "missionId": "mission123",
    "status": "IN_PROGRESS",
    "updatedBy": "user456"
  }
}
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |
